"use client";

import React from "react";
import { useState, useCallback, ChangeEvent, useEffect, useRef } from "react";
import { z } from "zod";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Seo from "@/components/Seo";
import { useAuth } from "@/hooks/useAuth";
import { startGeneration, useGenerationStatus } from "@/hooks/useResumeGeneration";
import ResumePreview from "@/components/ResumePreview";
import SuggestionList from "@/components/SuggestionList";
import { useResume } from "@/hooks/useResume";
import Toast from "@/components/Toast";
import TemplatePreviewThumbnail from "@/components/TemplatePreviewThumbnail";
import { ResumeTemplate, resumeTemplates } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";
import ResumeEditForm from "@/components/ResumeEditForm";
import { StructuredResumeData } from "@/types/resume-structured";
import ResumeBuilderLanding from "@/components/ResumeBuilderLanding";
import { createEmptyResumeStructure } from "@/utils/emptyResumeStructure";
import { convertToTemplateData, validateTemplateData } from "@/types/template-data";
import { motion, AnimatePresence } from 'framer-motion';
import { useStepNavigation, ThreeSteps } from '@/hooks/useStepNavigation';
import { useAnalytics } from '@/hooks/useAnalytics';
import { authFetch } from '@/lib/authFetch';

// --- Icon Components ---
const CheckIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" /></svg>
);
const UploadCloudIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg>
);
const FileTextIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
);
const XIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /></svg>
);
const ArrowRightIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" /></svg>
);
const Spinner = ({ className }: { className?: string }) => (
  <svg className={`animate-spin ${className}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
);
const ArrowLeftIcon = ({ className }: { className?: string }) => (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h17" /></svg>
);
const PencilIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z" /></svg>
);
const DownloadIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg>
);
const ExclamationTriangleIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" /></svg>
);
const TemplateIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" /></svg>
);

// --- Step Indicator Component ---
function StepIndicator({ step, currentStep, label }: { step: number; currentStep: number; label: string }) {
  const isActive = step === currentStep;
  const isCompleted = step < currentStep;

  return (
    <div className="flex flex-col items-center gap-2 relative text-center min-w-0">
      <div
        className={`w-10 h-10 flex items-center justify-center rounded-full border-2 font-bold transition-all duration-300 flex-shrink-0 ${
          isCompleted ? "bg-primary border-primary text-white" : isActive ? "border-primary text-primary scale-110" : "border-gray-300 text-gray-400"
        }`}
      >
        {isCompleted ? <CheckIcon className="w-6 h-6" /> : step}
      </div>
      <span className={`font-semibold text-xs sm:text-sm whitespace-nowrap ${isActive || isCompleted ? "text-gray-900" : "text-gray-500"}`}>{label}</span>
    </div>
  );
}

// --- Validation Schema ---
const resumeScratchSchema = z.object({
  fullName: z.string().min(1, "Nama lengkap wajib diisi."),
  professionalTitle: z.string().min(1, "Jabatan wajib diisi."),
  professionalSummary: z.string().min(1, "Ringkasan profesional wajib diisi."),
  mostRecentJob: z.object({
    title: z.string().min(1, "Jabatan terakhir wajib diisi."),
    company: z.string().min(1, "Perusahaan terakhir wajib diisi."),
    achievements: z.string().min(1, "Pencapaian utama wajib diisi."),
  }),
  skills: z.string().min(1, "Sebutkan setidaknya satu keahlian."),
});

type ResumeFormData = z.infer<typeof resumeScratchSchema>;

type FormErrors = Partial<Record<keyof ResumeFormData | 'mostRecentJob.title' | 'mostRecentJob.company' | 'mostRecentJob.achievements' | 'general', string>>;

// --- Bottom Sheet Component ---
interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

function BottomSheet({ isOpen, onClose, title, children }: BottomSheetProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleDragEnd = (event: any, info: any) => {
    // Close if dragged down more than 150px or with sufficient velocity
    if (info.offset.y > 150 || info.velocity.y > 500) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={onClose}
          />

          {/* Bottom Sheet */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: '0%' }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="fixed inset-x-0 bottom-0 z-50 md:hidden"
            drag="y"
            dragConstraints={{ top: 0, bottom: 0 }}
            dragElastic={{ top: 0, bottom: 0.5 }}
            onDragEnd={handleDragEnd}
            whileDrag={{ cursor: 'grabbing' }}
          >
            <div className="bg-white rounded-t-xl shadow-lg max-h-[80vh] flex flex-col">
              {/* Header with drag indicator */}
              <div className="flex flex-col items-center">
                {/* Drag Handle */}
                <div className="w-12 h-1 bg-gray-300 rounded-full mt-3" />
                
                <div className="flex items-center justify-between w-full p-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
                  <button
                    onClick={onClose}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                  >
                    <XIcon className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto p-4">
                {children}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

// --- Main Page Component ---
export default function ResumeBuilderPage() {
  const auth = useAuth();
  const { trackEvent, logError } = useAnalytics();
  
  // Initialize userPath - always start with null to avoid hydration mismatch
  const [userPath, setUserPath] = useState<'ai-powered' | 'manual' | null>(null);
  
  const [errors, setErrors] = useState<FormErrors>({});
  
  // Step navigation with browser history support
  const stepNav = useStepNavigation<ThreeSteps>({
    initialStep: userPath === 'manual' ? 3 : 1,
    totalSteps: 3,
    onStepValidation: async (currentStep, targetStep) => {
      if (currentStep === 1 && targetStep === 2) {
        // Validate step 1 completion
        if (resumeInputMethod === 'upload') {
          if (!hasUploadedResume) {
            setErrors({ general: "Silakan unggah CV/resume Anda" });
            return false;
          }
        } else if (resumeInputMethod === 'scratch') {
          const validationResult = resumeScratchSchema.safeParse(formData);
          if (!validationResult.success) {
            const newErrors: FormErrors = {};
            validationResult.error.errors.forEach((err) => {
              const path = err.path.join('.') as keyof FormErrors;
              newErrors[path] = err.message;
            });
            setErrors(newErrors);
            return false;
          }
        }
      }
      if (currentStep === 2 && targetStep === 3) {
        // Validate step 2 completion
        if (jobInputMethod === "text" && !jobText.trim()) {
          setToast({ show: true, message: "Deskripsi pekerjaan wajib diisi", type: "error" });
          return false;
        }
        if (jobInputMethod === "image" && !jobImage) {
          setToast({ show: true, message: "Gambar deskripsi pekerjaan wajib diunggah", type: "error" });
          return false;
        }
      }
      return true;
    },
    onStepChange: (newStep, prevStep) => {
      // Clear any previous errors when changing steps
      setErrors({});
      
      // Track step navigation
      trackEvent('Resume Builder Step Navigation', {
        from: prevStep,
        to: newStep,
        userPath: userPath || 'unknown'
      });
    },
  });

  // Extract step navigation methods and current step
  const { currentStep, goNext, goBack: originalGoBack, goToStep } = stepNav;
  
  // Custom goBack function that handles returning to path selection
  const goBack = useCallback(() => {
    // If we're on step 1 of AI-powered path or step 3 of manual path, go back to path selection
    if ((userPath === 'ai-powered' && currentStep === 1) || (userPath === 'manual' && currentStep === 3)) {
      // Clear URL parameters and reset to path selection
      const url = new URL(window.location.href);
      url.searchParams.delete('step');
      url.searchParams.delete('path');
      window.history.pushState({}, '', url.toString());
      
      // Reset state
      setUserPath(null);
      setStructuredData(null);
      setSelectedTemplate(null);
      setGenerationId(null);
      setErrors({});
      
      return;
    }
    
    // Otherwise use the original goBack function
    originalGoBack();
  }, [userPath, currentStep, originalGoBack]);

  // Step 3 template selection state
  const [selectedTemplate, setSelectedTemplate] = useState<ResumeTemplate | null>(null);

  // Step 4 live editing state
  const [structuredData, setStructuredData] = useState<StructuredResumeData | null>(null);
  const [liveHtmlContent, setLiveHtmlContent] = useState<string>("");
  const autoSaveTimer = useRef<NodeJS.Timeout | null>(null);
  const [hasRequiredFields, setHasRequiredFields] = useState<boolean>(false);

  // Overflow detection state
  const [isContentOverflowing, setIsContentOverflowing] = useState<boolean>(false);

  // Bottom sheet states
  const [isTemplateSheetOpen, setIsTemplateSheetOpen] = useState(false);
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);

  // Resume hook (handles upload, view, delete, existing resume detection)
  const {
    isUploading,
    uploadSuccess,
    error: resumeError,
    toast,
    existingResume,
    isLoading,
    isDeleting,
    isGettingResumeUrl,
    setToast,
    handleResumeUpload,
    handleViewResume,
    handleDeleteResume
  } = useResume(auth);

  const hasUploadedResume = uploadSuccess && !!existingResume;

  // Step 1 state
  const [resumeInputMethod, setResumeInputMethod] = useState<"upload" | "scratch">("upload");
  const [resumeFile, setResumeFile] = useState<File | null>(null);

  // Step 1 state (scratch)
  const [formData, setFormData] = useState<ResumeFormData>({
    fullName: "",
    professionalTitle: "",
    professionalSummary: "",
    mostRecentJob: {
      title: "",
      company: "",
      achievements: "",
    },
    skills: "",
  });

  // Step 2 state
  const [jobInputMethod, setJobInputMethod] = useState<"text" | "image">("text");
  const [jobText, setJobText] = useState("");
  const [jobImage, setJobImage] = useState<File | null>(null);

  // Step 3 generation state
  const [isDownloadingPdf, setIsDownloadingPdf] = useState(false);

  // Download resume as PDF using edge function
  const downloadResumePdf = async (resumeId: string) => {
    if (!resumeId) {
      setToast({ show: true, message: "Resume ID tidak ditemukan", type: "error" });
      return;
    }

    trackEvent('Resume Download Attempt', {
      format: 'pdf',
      resumeId: resumeId
    });

    setIsDownloadingPdf(true);

    try {
      const response = await authFetch(`/api/edge/resumes/${resumeId}/download`, {
        method: 'GET',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`API error: ${errorData.error || response.statusText}`);
      }

      const pdfBlob = await response.blob();

      const downloadLink = document.createElement('a');
      const url = window.URL.createObjectURL(pdfBlob);
      downloadLink.href = url;
      const fileName = `CV_Gigsta_${resumeId}`;
      downloadLink.download = `${fileName}.pdf`;
      document.body.appendChild(downloadLink);
      downloadLink.click();

      window.URL.revokeObjectURL(url);
      document.body.removeChild(downloadLink);

      trackEvent('Resume Downloaded', {
        format: 'pdf',
        resumeId: resumeId
      });
    } catch (err) {
      console.error('Error downloading resume PDF:', err);
      setToast({ show: true, message: 'Gagal mengunduh PDF. Silakan coba lagi.', type: 'error' });

      trackEvent('Resume Download Error', {
        error: err instanceof Error ? err.message : 'unknown_error',
        format: 'pdf',
        resumeId: resumeId
      });

      logError(err instanceof Error ? err : new Error('Failed to download resume PDF'), {
        feature: 'resume_builder',
        action: 'download_pdf'
      }, 'critical');
    } finally {
      setIsDownloadingPdf(false);
    }
  };

  const [generationId, setGenerationId] = useState<string | null>(null);
  const generation = useGenerationStatus(generationId);

  const handleDownload = React.useCallback(() => {
    if (!generationId) return;
    downloadResumePdf(generationId);
  }, [generationId]);

  // Initialize page state from URL parameters on mount
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);

    // Check if this is a page refresh by looking at navigation type
    // On page refresh, ignore path parameter and use initial step
    const isPageRefresh = window.performance &&
      window.performance.getEntriesByType('navigation').length > 0 &&
      (window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming).type === 'reload';

    if (isPageRefresh) {
      if (urlParams.has('path')) {
        // Remove the step parameter from URL
        urlParams.delete('path');
        const newUrl = new URL(window.location.href);
        newUrl.search = urlParams.toString();

        // Replace the current URL without adding to history
        window.history.replaceState(
          null,
          '',
          newUrl.toString()
        );

        return;
      }
    }

    const pathFromURL = urlParams.get('path');
    const stepFromURL = urlParams.get('step');

    // Initialize userPath from URL if available
    if (pathFromURL === 'ai-powered' || pathFromURL === 'manual') {
      setUserPath(pathFromURL);
    }

    // Only initialize if we have valid path and step parameters
    if ((pathFromURL === 'ai-powered' || pathFromURL === 'manual') && stepFromURL) {
      const step = parseInt(stepFromURL, 10);

      if (pathFromURL === 'manual' && step === 3) {
        // Initialize manual path with empty structure
        const emptyStructure = createEmptyResumeStructure();
        setStructuredData(emptyStructure);

        // Set default template
        const defaultTemplate = resumeTemplates.find(t => t.recommended) || resumeTemplates[0];
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, []); // Empty dependency array - only run once on mount

  // Handle browser back/forward navigation for path changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handlePopState = (event: PopStateEvent) => {
      const urlParams = new URLSearchParams(window.location.search);
      const pathFromURL = urlParams.get('path');
      const stepFromURL = urlParams.get('step');
      
      // If no path in URL, reset to path selection
      if (!pathFromURL) {
        setUserPath(null);
        setStructuredData(null);
        setSelectedTemplate(null);
        setGenerationId(null);
        setErrors({});
        return;
      }
      
      // Update userPath if it changed via browser navigation
      if (pathFromURL !== userPath && (pathFromURL === 'ai-powered' || pathFromURL === 'manual')) {
        setUserPath(pathFromURL);
        
        // Initialize manual path if navigating to it
        if (pathFromURL === 'manual' && stepFromURL === '3' && !structuredData) {
          const emptyStructure = createEmptyResumeStructure();
          setStructuredData(emptyStructure);
          
          const defaultTemplate = resumeTemplates.find(t => t.recommended) || resumeTemplates[0];
          setSelectedTemplate(defaultTemplate);
        }
      }
    };

    window.addEventListener('popstate', handlePopState);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [userPath, structuredData]);

  // Handle path selection
  const handlePathSelection = useCallback(async (path: 'ai-powered' | 'manual') => {
    setUserPath(path);
    
    // Add path to URL for proper browser history support
    const url = new URL(window.location.href);
    url.searchParams.set('path', path);
    
    if (path === 'manual') {
      // For manual path, skip to Step 3 with empty resume structure
      url.searchParams.set('step', '3');
      const emptyStructure = createEmptyResumeStructure();
      setStructuredData(emptyStructure);
      
      // Set default template
      const defaultTemplate = resumeTemplates.find(t => t.recommended) || resumeTemplates[0];
      setSelectedTemplate(defaultTemplate);
      
      // Update URL and navigate to step 3
      window.history.pushState({ path, step: 3 }, '', url.toString());
      await goToStep(3);
    } else {
      // For AI-powered path, start with Step 1
      url.searchParams.set('step', '1');
      window.history.pushState({ path, step: 1 }, '', url.toString());
      await goToStep(1);
    }
  }, [goToStep]);

  // Template selection handler
  const handleTemplateSelection = useCallback(async (template: ResumeTemplate) => {
    if (template.id === selectedTemplate?.id) return false;

    setSelectedTemplate(template);

    // Regenerate resume with new template
    if (structuredData) {
      try {
        // Convert to template data and validate required fields
        const templateData = convertToTemplateData(structuredData);
        const validation = validateTemplateData(templateData);

        if (!validation.isValid) {
          // Required fields are missing, skip template generation
          console.log("Skipping template generation - missing required fields:", validation.missingFields);
          setLiveHtmlContent("");
          setHasRequiredFields(false);
          return false;
        }

        // Required fields are present, proceed with template generation
        const html = fillResumeTemplate(template, structuredData);
        setLiveHtmlContent(html);
        setHasRequiredFields(true);

        // Save template selection to Supabase
        if (generationId && auth.user?.id) {
          try {
            const response = await fetch(`/api/resume/${generationId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                structured_data: structuredData,
                template_id: template.id
              })
            });

            if (response.ok) {
              console.log("Template selection saved to Supabase");
            } else {
              console.error("Failed to save template selection to Supabase");
            }
          } catch (error) {
            console.error("Error saving template selection:", error);
          }
        }

        setToast({ show: true, message: `Template berhasil diganti ke ${template.name}`, type: "success" });
        return true;
      } catch (error) {
        console.error('Gagal mengganti template:', error);
        setLiveHtmlContent("");
        setHasRequiredFields(false);
        setToast({ show: true, message: "Gagal mengganti template", type: "error" });
        return false;
      }
    }
    return false;
  }, [selectedTemplate, structuredData, generationId, auth.user?.id]);

  // Auto-save functionality
  const handleAutoSave = useCallback(async (data: StructuredResumeData) => {
    try {
      if (generationId && auth.user?.id) {
        const response = await fetch(`/api/resume/${generationId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            structured_data: data,
            template_id: selectedTemplate?.id || 'clean-professional'
          })
        });

        if (response.ok) {
          const result = await response.json();
          console.log("Resume auto-saved successfully:", result);
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Auto-save failed');
        }
      } else {
        console.log("No generation ID or user - skipping auto-save");
      }
    } catch (error) {
      console.error("Auto-save failed:", error);
      // Don't show error toast for auto-save failures to avoid annoying users
    }
  }, [auth.user?.id, generationId, selectedTemplate]);

  // Handle overflow detection from ResumePreview
  const handleOverflowDetected = useCallback((isOverflowing: boolean) => {
    setIsContentOverflowing(isOverflowing);

    // Show toast when overflow is detected
    if (isOverflowing) {
      setToast({
        show: true,
        message: "Konten melebihi satu halaman. Silakan kurangi konten agar muat dalam satu halaman",
        type: "error"
      });
    }
  }, []);

  // Live preview update with debouncing
  const updateLivePreview = useCallback(async (data: StructuredResumeData) => {
    if (!selectedTemplate) return;
    
    // Clear existing timer to prevent multiple timers running
    if (autoSaveTimer.current) {
      clearTimeout(autoSaveTimer.current);
    }
    
    const timer = setTimeout(async () => {
      try {
        // Convert to template data and validate required fields
        const templateData = convertToTemplateData(data);
        const validation = validateTemplateData(templateData);
        
        if (!validation.isValid) {
          // Required fields are missing, skip template generation
          console.log("Skipping template generation - missing required fields:", validation.missingFields);
          setLiveHtmlContent("");
          setHasRequiredFields(false);
          return;
        }
        
        // Required fields are present, proceed with template generation
        const html = fillResumeTemplate(selectedTemplate, data);
        setLiveHtmlContent(html);
        setHasRequiredFields(true);
      } catch (error) {
        console.error("Gagal memperbarui pratinjau langsung:", error);
        setLiveHtmlContent("");
        setHasRequiredFields(false);
      }
    }, 300); // 300ms debounce
    
    autoSaveTimer.current = timer;
  }, [selectedTemplate]);

  // Handle structured data changes
  const handleStructuredDataChange = useCallback((newData: StructuredResumeData) => {
    setStructuredData(newData);
    updateLivePreview(newData);
  }, [updateLivePreview]);

  // Process generation result to extract structured data
  useEffect(() => {
    if (generation.status === "done" && generationId) {
      // Try to use structured data from generation response first
      let finalStructuredData: StructuredResumeData;
      
      if (structuredData) {
        // Use structured data from local state
        finalStructuredData = structuredData;
      } else if (generation.structuredData) {
        // Use structured data from API response
        finalStructuredData = {
          ...generation.structuredData,
          metadata: {
            generatedAt: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            templateId: selectedTemplate?.id,
            ...generation.structuredData.metadata
          }
        };
      } else {
        // Fallback: create structured data from form data
        finalStructuredData = {
          personalInfo: {
            fullName: formData.fullName || "",
            email: "",
            phone: "",
            location: ""
          },
          professionalSummary: formData.professionalSummary || "",
          targetPosition: formData.professionalTitle || "",
          experiences: [{
            id: "exp-1",
            jobTitle: formData.mostRecentJob.title || "",
            company: formData.mostRecentJob.company || "",
            location: "",
            startDate: "",
            endDate: "",
            responsibilities: formData.mostRecentJob.achievements ?
              formData.mostRecentJob.achievements.split('\n').filter(line => line.trim()) :
              ["Tambahkan tanggung jawab"]
          }],
          education: [{
            id: "edu-1",
            degree: "",
            institution: "",
            graduationDate: ""
          }],
          skills: {
            categories: [{
              category: "Keahlian",
              skills: formData.skills ? formData.skills.split(',').map(s => s.trim()) : [""]
            }]
          },
          metadata: {
            generatedAt: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            templateId: selectedTemplate?.id
          }
        };
      }
      
      setStructuredData(finalStructuredData);
    } else if (generation.status === "error" && generationId) {
      // Handle error status
      const errorMessage = "Terjadi kesalahan saat membuat resume. Silakan coba lagi.";
      setToast({ show: true, message: errorMessage, type: "error" });
      console.error('Kesalahan pembuatan resume:', generation.error);
    }
  }, [generation.status, generationId, formData, selectedTemplate?.id, generation.status === "done" ? generation.structuredData : undefined]);

  // Initialize live preview when structured data and template are available
  useEffect(() => {
    if (structuredData && selectedTemplate) {
      updateLivePreview(structuredData);
    }
  }, [structuredData, selectedTemplate]); // Remove updateLivePreview from dependencies to avoid infinite loop

  // Cleanup timer on component unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimer.current) {
        clearTimeout(autoSaveTimer.current);
      }
    };
  }, []);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, field: keyof ResumeFormData | `mostRecentJob.${keyof ResumeFormData['mostRecentJob']}`) => {
    const { value } = e.target;
    const fieldParts = field.split('.');

    if (fieldParts.length > 1) {
        const [parent, child] = fieldParts as ['mostRecentJob', keyof ResumeFormData['mostRecentJob']];
        setFormData(prev => ({
            ...prev,
            [parent]: {
                ...prev[parent],
                [child]: value
            }
        }));
    } else {
        setFormData(prev => ({ ...prev, [field as keyof ResumeFormData]: value }));
    }

    if (errors[field as keyof typeof errors]) {
        setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };


  const handleFileChange = (files: FileList | null) => {
    if (files && files.length > 0) {
      setResumeFile(files[0]);
    }
  };

  const onDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    handleFileChange(event.dataTransfer.files);
  }, []);

  const onDragOver = (event: React.DragEvent<HTMLDivElement>) => event.preventDefault();

  return (
    <main className="min-h-screen flex flex-col pt-16 bg-gray-50">
      {/* Toast notifications */}
      {toast.show && (
        <Toast
          show={toast.show}
          message={toast.message}
          type={toast.type}
          onClose={() => setToast({ ...toast, show: false })}
        />
      )}
      <Seo
        title="AI CV Builder - Gigsta"
        description="Buat CV spesifik pekerjaan dengan AI."
        canonical="https://gigsta.io/resume-builder"
      />
      <Navbar auth={auth} />

      {/* Page Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-3 py-4 sm:px-6 sm:py-6 lg:px-8 lg:py-8">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">CV Builder</h1>
            <p className="mt-1 text-sm sm:text-base text-gray-600">
              {!userPath ? "Pilih cara terbaik untuk membuat CV Anda" : "Buat CV yang menonjol untuk pekerjaan impian Anda dengan bantuan AI"}
            </p>
        </div>
      </header>

      {/* Wizard */}
      <div className="flex-grow w-full max-w-6xl mx-auto px-3 py-4 sm:px-6 sm:py-6 lg:px-8 lg:py-8 pb-20 md:pb-4">
        {/* Show landing page if no path is selected */}
        {!userPath && (
          <div className="bg-white p-4 sm:p-8 lg:p-10 rounded-xl shadow-sm border border-gray-200">
            <ResumeBuilderLanding onSelectPath={handlePathSelection} />
          </div>
        )}

        {/* Show step wizard for selected path */}
        {userPath && (
          <>
            {/* Step Indicator Bar - only show for AI-powered path */}
            {userPath === 'ai-powered' && (
              <div className="flex items-center justify-center mb-8 relative">
                <div className="flex items-center justify-between w-full max-w-2xl">
                  <StepIndicator step={1} currentStep={currentStep} label="Unggah CV/Resume" />
                  <div className={`flex-1 h-0.5 mx-4 ${currentStep > 1 ? 'bg-primary' : 'bg-gray-300'}`}></div>
                  <StepIndicator step={2} currentStep={currentStep} label="Info Pekerjaan" />
                  <div className={`flex-1 h-0.5 mx-4 ${currentStep > 2 ? 'bg-primary' : 'bg-gray-300'}`}></div>
                  <StepIndicator step={3} currentStep={currentStep} label="Hasil & Edit" />
                </div>
              </div>
            )}

            {/* Step Content Card */}
            <div className="bg-white p-4 sm:p-8 lg:p-10 rounded-xl shadow-sm border border-gray-200">
          {currentStep === 1 && userPath === 'ai-powered' && (
            <div>
              <h2 className="text-2xl font-bold mb-2 text-gray-900">Langkah 1: Siapkan CV Anda</h2>
              <p className="text-gray-600 mb-8">Mulai dengan mengunggah CV/resume yang ada atau buat dari awal.</p>

              <div className="flex w-full p-1 bg-gray-100 rounded-xl border-2 border-gray-200 mb-8">
                <button
                  className={`flex-1 flex items-center justify-center gap-3 p-3 rounded-lg font-semibold transition-all text-sm sm:text-base ${resumeInputMethod === "upload" ? "bg-white shadow-sm text-primary" : "text-gray-600 hover:bg-gray-200"}`}
                  onClick={() => {
                    setResumeInputMethod("upload");
                    setErrors({});
                  }}
                >
                  <UploadCloudIcon className="w-5 h-5"/>
                  <span>Unggah CV/Resume</span>
                </button>
                <button
                  className={`flex-1 flex items-center justify-center gap-3 p-3 rounded-lg font-semibold transition-all text-sm sm:text-base ${resumeInputMethod === "scratch" ? "bg-white shadow-sm text-primary" : "text-gray-600 hover:bg-gray-200"}`}
                  onClick={() => {
                    setResumeInputMethod("scratch");
                    setErrors({});
                  }}
                >
                  <FileTextIcon className="w-5 h-5"/>
                  <span>Buat Dari Awal</span>
                </button>
              </div>

              {resumeInputMethod === "upload" ? (
                <div>
                  {isLoading ? (
                    <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                      <Spinner className="h-12 w-12 text-primary" />
                      <h3 className="mt-4 text-sm font-medium text-gray-900">Memeriksa CV/resume yang ada...</h3>
                      <p className="mt-1 text-xs text-gray-500">Mohon tunggu sebentar.</p>
                    </div>
                  ) : isUploading ? (
                    <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                      <Spinner className="h-12 w-12 text-primary" />
                      <h3 className="mt-4 text-sm font-medium text-gray-900">Mengunggah CV/resume...</h3>
                      <p className="mt-1 text-xs text-gray-500">Mohon tunggu sebentar.</p>
                    </div>
                  ) : !hasUploadedResume ? (
                    <div onDrop={onDrop} onDragOver={onDragOver} className="relative border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-primary transition-colors cursor-pointer">
                        <input type="file" accept=".pdf,.doc,.docx" onChange={handleResumeUpload} className="absolute inset-0 w-full h-full opacity-0 cursor-pointer" />
                        <UploadCloudIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900"><span className="text-primary">Klik untuk memilih file</span></h3>
                        <p className="mt-1 text-xs text-gray-500">PDF, DOC, DOCX (maks. 5MB)</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-4 border border-green-500 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-3">
                            <FileTextIcon className="w-6 h-6 text-green-700"/>
                            <span className="font-medium text-green-800">{existingResume?.fileName.split('_').pop()}</span>
                        </div>
                        <button onClick={() => handleDeleteResume()} className="text-gray-500 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed" disabled={isDeleting}>
                            {isDeleting ? <Spinner className="w-5 h-5 text-primary" /> : <XIcon className="w-5 h-5"/>}
                        </button>
                    </div>
                  )}
                </div>
              ) : (
                <form className="space-y-6 animate-fade-in">
                  {/* Personal Details */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">Nama Lengkap</label>
                      <input id="fullName" type="text" value={formData.fullName} onChange={(e) => handleInputChange(e, 'fullName')} placeholder="Contoh: Budi Sanjaya" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors.fullName ? 'border-red-500' : 'border-gray-300'}`} />
                      {errors.fullName && <p className="mt-1 text-xs text-red-600">{errors.fullName}</p>}
                    </div>
                    <div>
                      <label htmlFor="professionalTitle" className="block text-sm font-medium text-gray-700 mb-1">Jabatan / Peran yang Diinginkan</label>
                      <input id="professionalTitle" type="text" value={formData.professionalTitle} onChange={(e) => handleInputChange(e, 'professionalTitle')} placeholder="Contoh: Software Engineer" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors.professionalTitle ? 'border-red-500' : 'border-gray-300'}`} />
                      {errors.professionalTitle && <p className="mt-1 text-xs text-red-600">{errors.professionalTitle}</p>}
                    </div>
                  </div>

                  {/* Professional Summary */}
                  <div>
                    <label htmlFor="professionalSummary" className="block text-sm font-medium text-gray-700 mb-1">Ringkasan Profesional</label>
                    <textarea id="professionalSummary" value={formData.professionalSummary} onChange={(e) => handleInputChange(e, 'professionalSummary')} placeholder="Tuliskan 2-3 kalimat tentang keahlian, pengalaman, dan tujuan karir Anda."
                      className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors.professionalSummary ? 'border-red-500' : 'border-gray-300'}`} rows={4} />
                    {errors.professionalSummary && <p className="mt-1 text-xs text-red-600">{errors.professionalSummary}</p>}
                  </div>

                  {/* Most Recent Job */}
                  <div className="p-4 border border-gray-200 rounded-lg space-y-4">
                    <h3 className="font-semibold text-gray-800">Pengalaman Kerja Terakhir</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                       <div>
                         <label htmlFor="mostRecentJob.title" className="block text-sm font-medium text-gray-700 mb-1">Jabatan</label>
                         <input id="mostRecentJob.title" type="text" value={formData.mostRecentJob.title} onChange={(e) => handleInputChange(e, 'mostRecentJob.title')} placeholder="Contoh: Frontend Developer" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors['mostRecentJob.title'] ? 'border-red-500' : 'border-gray-300'}`} />
                         {errors['mostRecentJob.title'] && <p className="mt-1 text-xs text-red-600">{errors['mostRecentJob.title']}</p>}
                       </div>
                       <div>
                         <label htmlFor="mostRecentJob.company" className="block text-sm font-medium text-gray-700 mb-1">Perusahaan</label>
                         <input id="mostRecentJob.company" type="text" value={formData.mostRecentJob.company} onChange={(e) => handleInputChange(e, 'mostRecentJob.company')} placeholder="Contoh: PT Teknologi Maju" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors['mostRecentJob.company'] ? 'border-red-500' : 'border-gray-300'}`} />
                         {errors['mostRecentJob.company'] && <p className="mt-1 text-xs text-red-600">{errors['mostRecentJob.company']}</p>}
                       </div>
                    </div>
                     <div>
                        <label htmlFor="mostRecentJob.achievements" className="block text-sm font-medium text-gray-700 mb-1">Pencapaian Utama</label>
                        <textarea id="mostRecentJob.achievements" value={formData.mostRecentJob.achievements} onChange={(e) => handleInputChange(e, 'mostRecentJob.achievements')} placeholder="Gunakan poin-poin untuk menjelaskan 2-3 pencapaian utama Anda. Contoh: - Mengembangkan fitur X yang meningkatkan engagement user sebesar 20%."
                        className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors['mostRecentJob.achievements'] ? 'border-red-500' : 'border-gray-300'}`} rows={4} />
                        {errors['mostRecentJob.achievements'] && <p className="mt-1 text-xs text-red-600">{errors['mostRecentJob.achievements']}</p>}
                    </div>
                  </div>

                  {/* Skills */}
                  <div>
                    <label htmlFor="skills" className="block text-sm font-medium text-gray-700 mb-1">Keahlian Utama</label>
                    <input id="skills" type="text" value={formData.skills} onChange={(e) => handleInputChange(e, 'skills')} placeholder="Sebutkan 5-8 keahlian utama, pisahkan dengan koma. Contoh: React, TypeScript, Node.js" className={`w-full p-3 border rounded-lg shadow-sm focus:ring-primary focus:border-primary ${errors.skills ? 'border-red-500' : 'border-gray-300'}`} />
                    {errors.skills && <p className="mt-1 text-xs text-red-600">{errors.skills}</p>}
                  </div>
                </form>
              )}

              <div className="mt-10">
                <div className="flex justify-end">
                  <button onClick={goNext} className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 text-white p-3 sm:px-6 sm:py-3 rounded-lg font-semibold shadow-sm transition-colors">
                    <span className="inline">Selanjutnya</span>
                    <ArrowRightIcon className="w-5 h-5"/>
                  </button>
                </div>
                {/* General Validation Error */}
                {errors.general && (
                  <div className="mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                    {errors.general}
                  </div>
                )}
              </div>
            </div>
          )}

          {currentStep === 2 && userPath === 'ai-powered' && (
             <div>
              <h2 className="text-2xl font-bold mb-2 text-gray-900">Langkah 2: Informasi Pekerjaan</h2>
              <p className="text-gray-600 mb-8">Berikan kami detail tentang pekerjaan yang Anda lamar.</p>

              <div className="flex w-full p-1 bg-gray-100 rounded-xl border-2 border-gray-200 mb-8">
                 <button
                  className={`flex-1 flex items-center justify-center gap-3 p-3 rounded-lg font-semibold transition-all text-sm sm:text-base ${jobInputMethod === "text" ? "bg-white shadow-sm text-primary" : "text-gray-600 hover:bg-gray-200"}`}
                  onClick={() => {
                    setJobInputMethod("text");
                    setErrors({});
                  }}
                >
                  <FileTextIcon className="w-5 h-5"/>
                  <span>Deskripsi Teks</span>
                </button>
                <button
                  className={`flex-1 flex items-center justify-center gap-3 p-3 rounded-lg font-semibold transition-all text-sm sm:text-base ${jobInputMethod === "image" ? "bg-white shadow-sm text-primary" : "text-gray-600 hover:bg-gray-200"}`}
                  onClick={() => {
                    setJobInputMethod("image");
                    setErrors({});
                  }}
                >
                  <UploadCloudIcon className="w-5 h-5"/>
                  <span>Poster Gambar</span>
                </button>
              </div>

              {jobInputMethod === "text" ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tempel deskripsi pekerjaan</label>
                  <textarea value={jobText} onChange={(e) => setJobText(e.target.value)} placeholder="Jelaskan detail lowongan pekerjaan..." className="w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:ring-primary focus:border-primary" rows={8}/>
                </div>
              ) : (
                <div>
                  {!jobImage ? (
                     <div onDrop={(e) => { e.preventDefault(); setJobImage(e.dataTransfer.files?.[0] || null); }} onDragOver={onDragOver} className="relative border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-primary transition-colors cursor-pointer">
                        <input type="file" accept="image/*" onChange={(e) => setJobImage(e.target.files?.[0] || null)} className="absolute inset-0 w-full h-full opacity-0 cursor-pointer" />
                        <UploadCloudIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900"><span className="text-primary">Klik untuk memilih gambar</span></h3>
                        <p className="mt-1 text-xs text-gray-500">PNG, JPG, JPEG (maks. 5MB)</p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-4 border border-green-500 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-3">
                            <FileTextIcon className="w-6 h-6 text-green-700"/>
                            <span className="font-medium text-green-800">{jobImage.name}</span>
                        </div>
                        <button onClick={() => setJobImage(null)} className="text-gray-500 hover:text-gray-800">
                            <XIcon className="w-5 h-5"/>
                        </button>
                    </div>
                  )}
                </div>
              )}

              <div className="flex justify-between mt-10">
                <button onClick={goBack} className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 p-3 sm:px-6 sm:py-3 rounded-lg font-semibold shadow-sm transition-colors">
                    <ArrowLeftIcon className="w-5 h-5"/>
                    <span className="inline">Kembali</span>
                </button>
                <button
                  onClick={async () => {
                      // Basic validation for Step 2 inputs
                      if (jobInputMethod === "text" && !jobText.trim()) {
                        setToast({ show: true, message: "Deskripsi pekerjaan wajib diisi", type: "error" });
                        return;
                      }
                      if (jobInputMethod === "image" && !jobImage) {
                        setToast({ show: true, message: "Gambar deskripsi pekerjaan wajib diunggah", type: "error" });
                        return;
                      }

                      // Set default template (first recommended template)
                      const defaultTemplate = resumeTemplates.find(t => t.recommended) || resumeTemplates[0];
                      setSelectedTemplate(defaultTemplate);

                      // Move to Step 3 and start generation immediately
                      goNext();
                      
                      try {
                        // Prepare simplified parameters for startGeneration
                        const params = {
                          resumeInputMethod,
                          jobInputMethod,
                          jobText: jobInputMethod === "text" ? jobText : undefined,
                          jobImage: jobInputMethod === "image" ? (jobImage || undefined) : undefined,
                          formData: resumeInputMethod === "scratch" ? formData : undefined,
                          unauthenticatedResumeFile: resumeInputMethod === "upload" && existingResume?.unauthenticatedResumeFile ? existingResume.unauthenticatedResumeFile : undefined,
                          selectedTemplate: defaultTemplate
                        };

                        const generationId = await startGeneration(params);
                        setGenerationId(generationId);
                      } catch (e) {
                        console.error(e);
                        setGenerationId(null);
                        setToast({ show: true, message: (e as Error).message || "Gagal memulai AI", type: "error" });
                      }
                    }}
                  className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 text-white p-3 sm:px-6 sm:py-3 rounded-lg font-semibold shadow-sm transition-colors">
                  <span className="inline">Selanjutnya</span>
                  <ArrowRightIcon className="w-5 h-5"/>
                </button>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {userPath === 'manual' ? 'Edit CV Anda' : 'Langkah 3: Hasil & Edit'}
                  </h2>
                  <p className="text-gray-600 mt-2">
                    {userPath === 'manual'
                      ? "Mulai dari template kosong dan buat CV sesuai keinginan Anda"
                      : structuredData && generation.status === "done"
                        ? "Edit dan sesuaikan CV Anda"
                        : "AI sedang membuat CV Anda berdasarkan informasi yang telah diberikan"}
                  </p>
                </div>
              </div>
              
              {/* Show generation progress when not done - only for AI-powered path */}
              {!structuredData && userPath === 'ai-powered' && (
                <>
                  {generation.status === "processing" && (
                    <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
                      <Spinner className="h-12 w-12 text-primary mb-4" />
                      <h3 className="text-xl font-semibold text-gray-800">AI sedang bekerja...</h3>
                      <p className="text-gray-600 mt-2">CV Anda sedang dibuat. Proses ini mungkin memakan waktu sejenak</p>
                    </div>
                  )}

                  {generation.status === "error" && (
                    <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
                      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                        <XIcon className="w-8 h-8 text-red-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-red-800 mb-2">Oops! Terjadi Kesalahan</h3>
                      <p className="text-gray-600 mb-6 max-w-md">
                        {generation.error || "Maaf, terjadi kesalahan saat membuat CV Anda. Silakan coba lagi atau kembali ke langkah sebelumnya."}
                      </p>
                      <div className="flex gap-4">
                        <button
                          onClick={async () => {
                            // Retry generation with same parameters
                            try {
                              const params = {
                                resumeInputMethod,
                                jobInputMethod,
                                jobText: jobInputMethod === "text" ? jobText : undefined,
                                jobImage: jobInputMethod === "image" ? (jobImage || undefined) : undefined,
                                formData: resumeInputMethod === "scratch" ? formData : undefined,
                                unauthenticatedResumeFile: resumeInputMethod === "upload" && existingResume?.unauthenticatedResumeFile ? existingResume.unauthenticatedResumeFile : undefined,
                                selectedTemplate: selectedTemplate || undefined
                              };

                              const generationId = await startGeneration(params);
                              setGenerationId(generationId);
                            } catch (e) {
                              console.error(e);
                              setToast({ show: true, message: "Gagal memulai ulang AI", type: "error" });
                            }
                          }}
                          className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                        >
                          <span>Coba Lagi</span>
                        </button>
                        <button
                          onClick={() => {
                            setGenerationId(null);
                            setStructuredData(null);
                            setLiveHtmlContent("");
                            goBack();
                          }}
                          className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-6 py-3 rounded-lg font-semibold shadow-sm transition-colors"
                        >
                          <ArrowLeftIcon className="w-5 h-5"/>
                          <span>Kembali</span>
                        </button>
                      </div>
                    </div>
                  )}

                  {generation.status === "idle" && (
                    <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
                      <Spinner className="h-12 w-12 text-primary mb-4" />
                      <h3 className="text-xl font-semibold text-gray-800">Memulai proses...</h3>
                      <p className="text-gray-600 mt-2">Sedang menyiapkan untuk membuat CV Anda.</p>
                    </div>
                  )}

                  {/* Only show back button for processing and idle states */}
                  {(generation.status === "processing" || generation.status === "idle") && (
                    <div className="flex justify-between mt-8">
                      <button
                        onClick={() => {
                          setGenerationId(null);
                          // Clear existing structured data and preview when going back
                          setStructuredData(null);
                          setLiveHtmlContent("");
                          goBack();
                        }}
                        className="inline-flex items-center justify-center gap-2 border border-gray-300 hover:bg-gray-100 text-gray-800 px-4 py-2 rounded-lg font-semibold transition-colors"
                      >
                          <ArrowLeftIcon className="w-5 h-5"/>
                          <span>Kembali</span>
                      </button>
                    </div>
                  )}
                </>
              )}

              {/* Show editing interface when generation is complete */}
              {structuredData && (
                <>
                  <div className="animate-fade-in">
                    {/* Desktop Layout - Template Selector (hidden on mobile) */}
                    <div className="hidden md:block mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Pilih Template</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {resumeTemplates.map((template) => {
                          const isSelected = selectedTemplate?.id === template.id;
                          return (
                            <button
                              key={template.id}
                              onClick={() => handleTemplateSelection(template)}
                              className={`relative p-3 rounded-lg border transition-all duration-200 hover:shadow-sm ${
                                isSelected
                                  ? "border-primary bg-primary/5"
                                  : "border-gray-200 hover:border-gray-300"
                              }`}
                            >
                              <div className="text-center">
                                <div className="w-full aspect-[210/297] bg-white rounded mb-2 border border-gray-200 overflow-hidden p-1">
                                  <TemplatePreviewThumbnail template={template} />
                                </div>
                                <h4 className={`font-semibold text-sm ${isSelected ? 'text-primary' : 'text-gray-900'}`}>
                                  {template.name}
                                </h4>
                                <p className="text-xs text-gray-600 mt-1">{template.tokenCost} tokens</p>
                                {isSelected && (
                                  <div className="absolute top-1 right-1">
                                    <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                      <CheckIcon className="w-2.5 h-2.5 text-white" />
                                    </div>
                                  </div>
                                )}
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>

                    {/* Mobile-First Layout: Preview Only on Mobile, Side by Side on Desktop */}
                    <div className="md:gap-6 space-y-6">
                      {/* Preview Section - Always visible */}
                      <div className="md:order-1">
                        <div className="mb-3">
                          <h3 className="text-lg font-semibold text-gray-900">Pratinjau</h3>
                          <span className="text-sm text-gray-600">{selectedTemplate?.name}</span>
                        </div>



                        <div className="bg-gray-50 rounded-lg p-2 h-full w-full">
                          {liveHtmlContent ? (
                            <ResumePreview
                              htmlContent={liveHtmlContent}
                              onOverflowDetected={handleOverflowDetected}
                            />
                          ) : (
                            <div className="aspect-[210/297] bg-gray-100 rounded-lg flex items-center justify-center text-gray-400 border">
                              <div className="text-center px-4">
                                {userPath === 'manual' ? (
                                  <>
                                    <FileTextIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                                    <h4 className="text-sm font-medium text-gray-500 mb-1">CV Kosong</h4>
                                    <p className="text-xs text-gray-400">Isi data personal dan pengalaman Anda di bagian "Edit" untuk melihat pratinjau CV</p>
                                  </>
                                ) : (
                                  <>
                                    <Spinner className="w-8 h-8 mx-auto mb-2" />
                                    <span>Membuat pratinjau...</span>
                                  </>
                                )}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Overflow Warning - Below Preview */}
                        {isContentOverflowing && (
                          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg flex items-start gap-3">
                            <ExclamationTriangleIcon className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                            <div className="flex-1">
                              <p className="text-sm font-medium text-amber-800">
                                Konten Melebihi Satu Halaman
                              </p>
                              <p className="text-sm text-amber-700 mt-1">
                                Silakan sesuaikan konten agar muat dalam satu halaman
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                      
                      {/* Edit Section - Hidden on mobile, visible on desktop */}
                      <div className="hidden md:block md:order-2">
                        <div className="mb-3">
                          <h3 className="text-lg font-semibold text-gray-900">Edit CV</h3>
                          <p className="text-sm text-gray-600">Buat perubahan dan lihat hasilnya secara langsung</p>
                        </div>
                        <div className="rounded-lg h-full overflow-y-auto">
                          <ResumeEditForm
                            data={structuredData}
                            onChange={handleStructuredDataChange}
                            onAutoSave={handleAutoSave}
                            autoSaveDelay={1000}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Mobile Bottom Navigation */}
                  <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-3 py-4 pb-safe sm:px-4 md:hidden">
                    <div className="flex justify-between items-center gap-2 sm:gap-3">
                      {/* Template Button */}
                      <button
                        onClick={() => setIsTemplateSheetOpen(true)}
                        className="flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg font-medium transition-colors"
                      >
                        <TemplateIcon className="w-5 h-5" />
                        <span className="hidden sm:inline">Template</span>
                      </button>

                      {/* Edit Button */}
                      <button
                        onClick={() => setIsEditSheetOpen(true)}
                        className="flex-1 flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg font-medium transition-colors"
                      >
                        <PencilIcon className="w-5 h-5" />
                        <span className="hidden sm:inline">Edit</span>
                      </button>

                      {/* Download Button */}
                      {generation.status === "done" ? (
                        hasRequiredFields ? (
                          <button
                            onClick={handleDownload}
                            disabled={isDownloadingPdf}
                            className="flex-1 flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-3 rounded-lg font-medium transition-colors"
                          >
                            {isDownloadingPdf ? (
                              <>
                                <Spinner className="w-5 h-5" />
                                <span className="hidden sm:inline">Mengunduh...</span>
                              </>
                            ) : (
                              <>
                                <DownloadIcon className="w-5 h-5" />
                                <span className="hidden sm:inline">Unduh</span>
                              </>
                            )}
                          </button>
                        ) : (
                          <button
                            disabled
                            className="flex-1 flex items-center justify-center gap-2 bg-gray-400 cursor-not-allowed text-white px-4 py-3 rounded-lg font-medium"
                          >
                            <DownloadIcon className="w-5 h-5" />
                            <span className="hidden sm:inline">Unduh</span>
                          </button>
                        )
                      ) : (
                        hasRequiredFields ? (
                          <button
                            onClick={handleDownload}
                            disabled={!generationId}
                            className="flex-1 flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-3 rounded-lg font-medium transition-colors"
                          >
                            <DownloadIcon className="w-5 h-5" />
                            <span className="hidden sm:inline">Buat</span>
                          </button>
                        ) : (
                          <button
                            disabled
                            className="flex-1 flex items-center justify-center gap-2 bg-gray-400 cursor-not-allowed text-white px-4 py-3 rounded-lg font-medium"
                          >
                            <DownloadIcon className="w-5 h-5" />
                            <span className="hidden sm:inline">Buat</span>
                          </button>
                        )
                      )}
                    </div>
                  </div>

                  {/* Desktop Action Buttons */}
                  <div className="hidden md:flex justify-end mt-6">
                    {generation.status === "done" ? (
                      hasRequiredFields ? (
                        <button
                          onClick={handleDownload}
                          disabled={isDownloadingPdf}
                          className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                        >
                          {isDownloadingPdf ? (
                            <>
                              <Spinner className="w-5 h-5" />
                              <span>Mengunduh...</span>
                            </>
                          ) : (
                            <>
                              <DownloadIcon className="w-5 h-5" />
                              <span>Unduh PDF</span>
                            </>
                          )}
                        </button>
                      ) : (
                        <button
                          disabled
                          className="inline-flex items-center justify-center gap-2 bg-gray-400 cursor-not-allowed text-white px-6 py-3 rounded-lg font-semibold"
                        >
                          <DownloadIcon className="w-5 h-5" />
                          <span>Unduh PDF</span>
                        </button>
                      )
                    ) : (
                      hasRequiredFields ? (
                        <button
                          onClick={handleDownload}
                          disabled={!generationId}
                          className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                        >
                          <DownloadIcon className="w-5 h-5" />
                          <span>Buat & Unduh</span>
                        </button>
                      ) : (
                        <button
                          disabled
                          className="inline-flex items-center justify-center gap-2 bg-gray-400 cursor-not-allowed text-white px-6 py-3 rounded-lg font-semibold"
                        >
                          <DownloadIcon className="w-5 h-5" />
                          <span>Buat & Unduh</span>
                        </button>
                      )
                    )}
                  </div>

                  {/* Template Selection Bottom Sheet */}
                  <BottomSheet
                    isOpen={isTemplateSheetOpen}
                    onClose={() => setIsTemplateSheetOpen(false)}
                    title="Pilih Template"
                  >
                    <div className="grid grid-cols-2 gap-4">
                      {resumeTemplates.map((template) => {
                        const isSelected = selectedTemplate?.id === template.id;
                        return (
                          <button
                            key={template.id}
                            onClick={async () => {
                              if (template.id === selectedTemplate?.id) {
                                setIsTemplateSheetOpen(false);
                                return;
                              }

                              const success = await handleTemplateSelection(template);
                              if (success) {
                                setIsTemplateSheetOpen(false);
                              }
                            }}
                            className={`relative p-3 rounded-lg border transition-all duration-200 hover:shadow-sm ${
                              isSelected
                                ? "border-primary bg-primary/5"
                                : "border-gray-200 hover:border-gray-300"
                            }`}
                          >
                            <div className="text-center">
                              <div className="w-full aspect-[210/297] bg-white rounded mb-2 border border-gray-200 overflow-hidden p-1">
                                <TemplatePreviewThumbnail template={template} />
                              </div>
                              <h4 className={`font-semibold text-xs ${isSelected ? 'text-primary' : 'text-gray-900'}`}>
                                {template.name}
                              </h4>
                              <p className="text-xs text-gray-600 mt-1">{template.tokenCost} tokens</p>
                              {isSelected && (
                                <div className="absolute top-1 right-1">
                                  <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                    <CheckIcon className="w-2.5 h-2.5 text-white" />
                                  </div>
                                </div>
                              )}
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </BottomSheet>

                  {/* Edit Form Bottom Sheet */}
                  <BottomSheet
                    isOpen={isEditSheetOpen}
                    onClose={() => setIsEditSheetOpen(false)}
                    title="Edit CV"
                  >
                    <ResumeEditForm
                      data={structuredData}
                      onChange={handleStructuredDataChange}
                      onAutoSave={handleAutoSave}
                      autoSaveDelay={1000}
                    />
                  </BottomSheet>
                </>
              )}
            </div>
          )}
            </div>
          </>
        )}
      </div>

      <Footer />

    </main>
  );
}
